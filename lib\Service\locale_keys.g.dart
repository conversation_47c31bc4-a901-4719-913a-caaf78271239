// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const Store = 'Store';
  static const Settings = 'Settings';
  static const SelectLanguage = 'SelectLanguage';
  static const Help = 'Help';
  static const HelpText = 'HelpText';
  static const HelpDialog = 'HelpDialog';
  static const Exit = 'Exit';
  static const Profile = 'Profile';
  static const Skills = 'Skills';
  static const Skill = 'Skill';
  static const Attributes = 'Attributes';
  static const Attribute = 'Attribute';
  static const CreateSkill = 'CreateSkill';
  static const CreateAttribute = 'CreateAttribute';
  static const WeeklyProgress = 'WeeklyProgress';
  static const WeeklyWorkHour = 'WeeklyWorkHour';
  static const Add = 'Add';
  static const Buy = 'Buy';
  static const OnePiece = 'OnePiece';
  static const Create = 'Create';
  static const Name = 'Name';
  static const Cancel = 'Cancel';
  static const TraitNameEmpty = 'TraitNameEmpty';
  static const RoutineStartDateError = 'RoutineStartDateError';
  static const EditTask = 'EditTask';
  static const EditRoutine = 'EditRoutine';
  static const Edit = 'Edit';
  static const AddTask = 'AddTask';
  static const TaskName = 'TaskName';
  static const Hour = 'Hour';
  static const h = 'h';
  static const Minute = 'Minute';
  static const m = 'm';
  static const Second = 'Second';
  static const s = 's';
  static const Warning = 'Warning';
  static const Info = 'Info';
  static const Success = 'Success';
  static const NoTaskForToday = 'NoTaskForToday';
  static const RoutineForFuture = 'RoutineForFuture';
  static const Delete = 'Delete';
  static const Failed = 'Failed';
  static const ChangeDate = 'ChangeDate';
  static const Times = 'Times';
  static const AllTime = 'AllTime';
  static const DaysInProgress = 'DaysInProgress';
  static const AvarageDay = 'AvarageDay';
  static const Routines = 'Routines';
  static const FutureRoutines = 'FutureRoutines';
  static const Completed = 'Completed';
  static const NameEmpty = 'NameEmpty';
  static const AddItem = 'AddItem';
  static const EditItem = 'EditItem';
  static const TaskDescription = 'TaskDescription';
  static const SelectTime = 'SelectTime';
  static const Average = 'Average';
  static const InADay = 'InADay';
  static const BestHour = 'BestHour';
  static const BestDay = 'BestDay';
  static const LongestStreak = 'LongestStreak';
  static const CurrentStreak = 'CurrentStreak';
  static const Streak = 'Streak';
  static const Day = 'Day';
  static const Today = 'Today';
  static const RecentLogs = 'RecentLogs';
  static const Cancelled = 'Cancelled';
  static const InProgress = 'InProgress';
  static const NotStarted = 'NotStarted';
  static const TotalTime = 'TotalTime';
  static const Priority = 'Priority';
  static const HighPriority = 'HighPriority';
  static const MediumPriority = 'MediumPriority';
  static const LowPriority = 'LowPriority';
  static const CurrentProgress = 'CurrentProgress';
  static const Minutes = 'Minutes';
  static const Count = 'Count';
  static const Save = 'Save';
  static const DeleteAllData = 'DeleteAllData';
  static const DeleteAllDataWarning = 'DeleteAllDataWarning';
  static const DeleteAllDataSuccess = 'DeleteAllDataSuccess';
  static const Yes = 'Yes';
  static const Hide = 'Hide';
  static const Show = 'Show';
  static const storage_permission_required = 'storage_permission_required';
  static const storage_access_error = 'storage_access_error';
  static const downloads_access_error = 'downloads_access_error';
  static const backup_created_successfully = 'backup_created_successfully';
  static const backup_creation_error = 'backup_creation_error';
  static const backup_restored_successfully = 'backup_restored_successfully';
  static const backup_restore_cancelled = 'backup_restore_cancelled';
  static const backup_restore_error = 'backup_restore_error';
  static const task_completed_title = 'task_completed_title';
  static const task_completed_desc = 'task_completed_desc';
  static const item_expired_title = 'item_expired_title';
  static const item_expired_desc = 'item_expired_desc';
  static const notification_permission_required = 'notification_permission_required';
  static const alarm_permission_required = 'alarm_permission_required';
  static const notification_channel_name = 'notification_channel_name';
  static const notification_channel_desc = 'notification_channel_desc';
  static const ThemeSelection = 'ThemeSelection';
  static const ThemeSelectionSubtitle = 'ThemeSelectionSubtitle';
  static const DataManagement = 'DataManagement';
  static const DataManagementSubtitle = 'DataManagementSubtitle';
  static const ExportData = 'ExportData';
  static const ImportData = 'ImportData';
  static const Subtasks = 'Subtasks';
  static const AddSubtask = 'AddSubtask';
  static const EditSubtask = 'EditSubtask';
  static const SubtaskEmpty = 'SubtaskEmpty';
  static const HideSubtasks = 'HideSubtasks';
  static const ShowSubtasks = 'ShowSubtasks';
  static const Location = 'Location';
  static const EnterLocation = 'EnterLocation';
  static const ShowOnMap = 'ShowOnMap';
  static const Title = 'Title';
  static const Description = 'Description';
  static const EnterTitle = 'EnterTitle';
  static const EnterDescription = 'EnterDescription';
  static const AddManualLog = 'AddManualLog';
  static const NoLogsYet = 'NoLogsYet';
  static const Date = 'Date';
  static const Time = 'Time';
  static const Progress = 'Progress';
  static const EnterCount = 'EnterCount';
  static const Hours = 'Hours';
  static const CompletedTask = 'CompletedTask';
  static const EditLog = 'EditLog';
  static const Status = 'Status';
  static const Category = 'Category';
  static const Categories = 'Categories';
  static const CreateCategory = 'CreateCategory';
  static const EditCategory = 'EditCategory';
  static const CategoryName = 'CategoryName';
  static const CategoryNameEmpty = 'CategoryNameEmpty';
  static const SelectColor = 'SelectColor';
  static const NoCategories = 'NoCategories';
  static const NoCategory = 'NoCategory';
  static const CreateNewCategory = 'CreateNewCategory';
  static const DeleteCategory = 'DeleteCategory';
  static const DeleteCategoryConfirmation = 'DeleteCategoryConfirmation';
  static const NoCategoriesYet = 'NoCategoriesYet';
  static const AddCategory = 'AddCategory';
  static const NoTasksInCategory = 'NoTasksInCategory';
  static const Tomorrow = 'Tomorrow';
  static const Yesterday = 'Yesterday';
  static const AllTasks = 'AllTasks';
  static const NoTasksYet = 'NoTasksYet';
  static const Tasks = 'Tasks';
  static const Close = 'Close';
  static const ShowCompleted = 'ShowCompleted';
  static const HideCompleted = 'HideCompleted';
  static const SearchTasks = 'SearchTasks';
  static const Search = 'Search';
  static const Filters = 'Filters';

}
