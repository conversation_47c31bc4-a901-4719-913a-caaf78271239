{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\KARGALAR\\Projeler\\Flutter\\gamify_todo\\android\\app\\.cxx\\Debug\\5m4d1h6i\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\KARGALAR\\Projeler\\Flutter\\gamify_todo\\android\\app\\.cxx\\Debug\\5m4d1h6i\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}