{"Store": "Store", "Settings": "Settings", "SelectLanguage": "Select Language", "Help": "Help", "HelpText": "Tips about the application.", "HelpDialog": "With this application, you can create, edit, and delete your to-do list.\n1 hour of work equals 1 credit.\nIf routine days are selected, that task is saved as a routine. If not selected, it is saved as a normal task.", "Exit": "Exit", "Profile": "Profile", "Skills": "Skills", "Skill": "Skill", "Attributes": "Attributes", "Attribute": "Attribute", "CreateSkill": "Create Skill", "CreateAttribute": "Create Attribute", "WeeklyProgress": "Weekly Progress", "WeeklyWorkHour": "Weekly Work Hour", "Add": "Add", "Buy": "Buy", "OnePiece": "{} Piece", "Create": "Create", "Name": "Name", "Cancel": "Cancel", "TraitNameEmpty": "Trait name cannot be empty.", "RoutineStartDateError": "Routine start date cannot be before today.", "EditTask": "Edit Task", "EditRoutine": "<PERSON>", "Edit": "Edit", "AddTask": "Add Task", "TaskName": "Task Name", "Hour": "Hour", "h": "h", "Minute": "Minute", "m": "m", "Second": "Second", "s": "s", "Warning": "Warning", "Info": "Info", "Success": "Success", "NoTaskForToday": "No task for today", "RoutineForFuture": "Routine cannot be interacted with because it is in the future", "Delete": "Delete", "Failed": "Failed", "ChangeDate": "Change Date", "Times": "Times", "AllTime": "All Time", "DaysInProgress": "days in progress", "AvarageDay": "Average day", "Routines": "Routines", "FutureRoutines": "Future Routines", "Completed": "Completed", "NameEmpty": "Name cannot be empty.", "AddItem": "Add Item", "EditItem": "<PERSON>em", "TaskDescription": "Task Description", "SelectTime": "Select Time", "Average": "Average", "InADay": "In a day", "BestHour": "Best Hour", "BestDay": "Best Day", "LongestStreak": "Longest Streak", "CurrentStreak": "Current Streak", "Streak": "Streaks", "Day": "Day", "Today": "Today", "RecentLogs": "Recent Logs", "Cancelled": "Cancelled", "InProgress": "In Progress", "NotStarted": "Not Started", "TotalTime": "Total Time", "Priority": "Priority", "HighPriority": "High", "MediumPriority": "Medium", "LowPriority": "Low", "CurrentProgress": "Current Progress", "Minutes": "Minutes", "Count": "Count", "Save": "Save", "DeleteAllData": "Delete All Data", "DeleteAllDataWarning": "Are you sure you want to delete all data?", "DeleteAllDataSuccess": "All data has been deleted", "Yes": "Yes", "Hide": "<PERSON>de", "Show": "Show", "storage_permission_required": "Storage permission is required for backup", "storage_access_error": "Could not access storage directory", "downloads_access_error": "Could not access Downloads directory", "backup_created_successfully": "Backup created successfully in Downloads folder", "backup_creation_error": "Error creating backup: {}", "backup_restored_successfully": "Backup restored successfully", "backup_restore_cancelled": "Backup restore cancelled", "backup_restore_error": "Error restoring backup: {}", "task_completed_title": "🎉 {} Completed", "task_completed_desc": "Total duration: {}", "item_expired_title": "⚠️ {} Time Expired", "item_expired_desc": "Do not exceed the time limit!", "notification_permission_required": "Notification permission is required", "alarm_permission_required": "Alarm permission is required", "notification_channel_name": "Task Completion", "notification_channel_desc": "Notifications for completed tasks", "ThemeSelection": "Theme Selection", "ThemeSelectionSubtitle": "Change Dark/Light theme", "DataManagement": "Data Management", "DataManagementSubtitle": "Backup or restore your data", "ExportData": "Backup Data", "ImportData": "Restore from Backup", "Subtasks": "Subtasks", "AddSubtask": "Add Subtask", "EditSubtask": "Edit Subtask", "SubtaskEmpty": "Subtask cannot be empty.", "HideSubtasks": "Hide subtasks", "ShowSubtasks": "Show subtasks", "Title": "Title", "Description": "Description", "EnterTitle": "Enter title", "EnterDescription": "Enter description", "Location": "Location", "EnterLocation": "Enter location", "ShowOnMap": "Show on map", "AddManualLog": "Add Manual Log", "NoLogsYet": "No logs yet", "Date": "Date", "Time": "Time", "Progress": "Progress", "EnterCount": "Enter count", "Hours": "Hours", "CompletedTask": "Completed task", "EditLog": "Edit Log", "Status": "Status", "Category": "Category", "Categories": "Categories", "CreateCategory": "Create Category", "EditCategory": "Edit Category", "CategoryName": "Category Name", "CategoryNameEmpty": "Category name cannot be empty.", "SelectColor": "Select Color", "NoCategories": "No categories available", "NoCategory": "No Category", "CreateNewCategory": "Create New Category", "DeleteCategory": "Delete Category", "DeleteCategoryConfirmation": "Are you sure you want to delete this category?", "NoCategoriesYet": "No categories yet", "AddCategory": "Add Category", "NoTasksInCategory": "No tasks in this category", "Tomorrow": "Tomorrow", "Yesterday": "Yesterday", "AllTasks": "All Tasks", "NoTasksYet": "No tasks yet", "Tasks": "Tasks", "Close": "Close", "ShowCompleted": "Show completed", "HideCompleted": "Hide completed", "SearchTasks": "Search tasks...", "Search": "Search", "Filters": "Filters"}