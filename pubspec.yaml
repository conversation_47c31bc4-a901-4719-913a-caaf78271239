name: next_level
description: "Gamify your todo list"

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: ^3.5.3


dependencies:
  flutter:
    sdk: flutter

  # Get images from assets
  path_provider: ^2.1.1

  # Date Formatter
  intl: ^0.19.0

  # Font style
  google_fonts: ^6.1.0

  # Notification
  flutter_local_notifications: ^19.0.0
  timezone: ^0.10.0

  # Save Data
  hive_flutter: ^1.1.0
  hive_generator: ^2.0.1
  shared_preferences: ^2.3.3

  # Svg support
  flutter_svg: ^2.0.9

  # State Management
  provider: ^6.1.2
  equatable: ^2.0.5

  # Responsive
  flutter_screenutil: ^5.9.0

  # GetX
  get: ^4.6.6

  # Splash Screen
  flutter_native_splash: ^2.4.1

  # Permissions
  permission_handler: ^11.0.1

  # Get device info
  device_info_plus: ^9.1.1

  # Rename App
  rename_app: ^1.6.3

  # Auto Size Text
  auto_size_text: ^3.0.0

  # Duration Picker
  duration_picker: ^1.2.0

  # Emoji Picker
  emoji_picker_flutter: ^3.1.0

  # Slide Action
  flutter_slidable: ^3.1.1

  # Charts
  fl_chart: ^0.69.1

  # http
  dio: ^5.7.0
  connectivity_plus: ^6.1.3

  # Localization
  easy_localization: ^3.0.7

  # Windows
  window_manager: ^0.4.3

  # Background Service
  flutter_background_service: ^5.1.0

  # calender
  table_calendar: ^3.1.3

  # Home Widget
  home_widget: ^0.7.0

  # File Picker
  file_picker: ^8.1.7
  path: ^1.9.0

  # Firebase
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.2
  cloud_firestore: ^5.6.6

  # URL Launcher
  url_launcher: ^6.3.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.8

  # Change App Package Name
  change_app_package_name: ^1.4.0

  flutter_lints: ^4.0.0

  flutter_launcher_icons: ^0.14.2




# Splash Screen Settings
flutter_native_splash:
  color: "#030303" # Arka plan rengi
  image: assets/logo/splash600.png # Splash görüntüsü
  android_12:
    image: assets/logo/splash1152.png # Android 12+ için splash görüntüsü
    color: "#030303" # Arka plan rengi

# App Icon Settings
flutter_icons:
  android: true
  ios: true
  image_path: "assets/logo/logo.png"
  adaptive_icon_background: "#030303"
  adaptive_icon_foreground: "assets/logo/logo_transparent.png"
  min_sdk_android: 21
  remove_alpha_ios: true

flutter:
  uses-material-design: true
  assets:
    # Translation files
    - assets/translations/
    # Logos
    - assets/logo/
    # Sounds
    - assets/sounds/

  # fonts:
  #   - family: FacelogIcon
  #     fonts:
  #       - asset: assets\icons\FacelogIcon.ttf