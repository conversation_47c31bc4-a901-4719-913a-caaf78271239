// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBX9gaJbQEq9iw1vk0unQDbCzp4WrlXgaU',
    appId: '1:769104090883:web:67a9daa791d772bc7bbb36',
    messagingSenderId: '769104090883',
    projectId: 'nextlevel-57a28',
    authDomain: 'nextlevel-57a28.firebaseapp.com',
    storageBucket: 'nextlevel-57a28.firebasestorage.app',
    measurementId: 'G-Q43QVF460N',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDPjn0Lf09xN-0P0FprkJfyCmsI4G6x6bs',
    appId: '1:769104090883:android:60e654e7873dc0047bbb36',
    messagingSenderId: '769104090883',
    projectId: 'nextlevel-57a28',
    storageBucket: 'nextlevel-57a28.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBBBtScbwWhJ-MDw3g81KcTvCyNPYbcvYs',
    appId: '1:769104090883:ios:aadcf969ce43951b7bbb36',
    messagingSenderId: '769104090883',
    projectId: 'nextlevel-57a28',
    storageBucket: 'nextlevel-57a28.firebasestorage.app',
    iosBundleId: 'app.nextlevel',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBBBtScbwWhJ-MDw3g81KcTvCyNPYbcvYs',
    appId: '1:769104090883:ios:c148b710f166faf07bbb36',
    messagingSenderId: '769104090883',
    projectId: 'nextlevel-57a28',
    storageBucket: 'nextlevel-57a28.firebasestorage.app',
    iosBundleId: 'com.example.gamifyTodo',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBX9gaJbQEq9iw1vk0unQDbCzp4WrlXgaU',
    appId: '1:769104090883:web:39a81108dba06c527bbb36',
    messagingSenderId: '769104090883',
    projectId: 'nextlevel-57a28',
    authDomain: 'nextlevel-57a28.firebaseapp.com',
    storageBucket: 'nextlevel-57a28.firebasestorage.app',
    measurementId: 'G-1CJN66Y1GN',
  );
}
