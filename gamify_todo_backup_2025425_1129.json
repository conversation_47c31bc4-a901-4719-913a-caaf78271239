{"userBox": {"0": {"id": 0, "email": "", "password": "", "credit_progress": "00:11:26", "user_credit": 9}}, "itemBox": {"1": {"id": 1, "title": "Game", "type": "TIMER", "current_duration": "00:00:00", "add_duration": "01:00:00", "current_count": null, "credit": 5, "is_timer_active": false, "add_count": 1}, "2": {"id": 2, "title": "Movie ", "type": "COUNTER", "current_duration": null, "add_duration": "01:30:00", "current_count": 0, "credit": 4, "is_timer_active": null, "add_count": 1}, "3": {"id": 3, "title": "Episode", "type": "COUNTER", "current_duration": null, "add_duration": "00:30:00", "current_count": 0, "credit": 3, "is_timer_active": null, "add_count": 1}, "4": {"id": 4, "title": "YouTube", "type": "TIMER", "current_duration": "00:00:00", "add_duration": "00:30:00", "current_count": null, "credit": 3, "is_timer_active": false, "add_count": 1}}, "traitBox": {"2": {"id": 2, "title": "Book", "icon": "📖", "color": "ff4caf50", "type": "SKILL", "is_archived": false}, "3": {"id": 3, "title": "Culture", "icon": "🎭", "color": "ffff9800", "type": "ATTRIBUTE", "is_archived": false}, "4": {"id": 4, "title": "Meditation", "icon": "🧘‍♂️", "color": "ff2196f3", "type": "SKILL", "is_archived": false}, "5": {"id": 5, "title": "Intelligance", "icon": "🧠", "color": "ff1773db", "type": "ATTRIBUTE", "is_archived": false}, "6": {"id": 6, "title": "Wisdom", "icon": "🪬", "color": "ff1773db", "type": "ATTRIBUTE", "is_archived": false}, "7": {"id": 7, "title": "Money", "icon": "💸", "color": "ff9e9e9e", "type": "ATTRIBUTE", "is_archived": false}, "8": {"id": 8, "title": "Body", "icon": "💪", "color": "ff4caf50", "type": "ATTRIBUTE", "is_archived": false}}, "routineBox": {"1": {"id": 1, "title": "🧘Meditation", "description": null, "type": "TIMER", "created_date": "2025-04-24T17:08:24.168", "start_date": "2025-04-24T00:00:00.000Z", "time": null, "is_notification_on": false, "is_alarm_on": false, "remaining_duration": "00:10:00", "target_count": 1, "repeat_days": [0, 1, 2, 3, 4, 5, 6], "attribute_id_list": [6], "skill_id_list": [2, 4], "is_archived": false, "priority": 3}, "2": {"id": 2, "title": "☕Baristacılık", "description": null, "type": "CHECKBOX", "created_date": "2025-04-24T18:10:40.765", "start_date": "2025-04-24T18:08:43.244", "time": null, "is_notification_on": false, "is_alarm_on": false, "remaining_duration": "09:00:00", "target_count": 1, "repeat_days": [1, 2, 3, 4, 5, 6], "attribute_id_list": [7], "skill_id_list": [], "is_archived": false, "priority": 3}, "3": {"id": 3, "title": "🍽️Öne Meal", "description": null, "type": "CHECKBOX", "created_date": "2025-04-24T23:08:17.608", "start_date": "2025-04-24T23:04:44.953", "time": null, "is_notification_on": false, "is_alarm_on": false, "remaining_duration": "00:00:00", "target_count": 1, "repeat_days": [0, 3, 1, 4, 2, 6, 5], "attribute_id_list": [8], "skill_id_list": [], "is_archived": false, "priority": 3}}, "taskBox": {"1": {"id": 1, "routine_id": null, "title": "<PERSON><PERSON>", "description": "with <PERSON><PERSON><PERSON>", "type": "CHECKBOX", "task_date": "2025-05-06T00:00:00.000Z", "time": "21:00:00", "is_notification_on": false, "is_alarm_on": true, "current_duration": null, "remaining_duration": "00:00:00", "current_count": null, "target_count": 1, "attribute_id_list": [], "skill_id_list": [], "status": null, "priority": 2, "is_timer_active": null, "subtasks": null, "location": "IF performance Tunalı"}, "2": {"id": 2, "routine_id": 1, "title": "🧘Meditation", "description": null, "type": "TIMER", "task_date": "2025-04-24T00:00:00.000Z", "time": null, "is_notification_on": false, "is_alarm_on": false, "current_duration": "00:10:12", "remaining_duration": "00:10:00", "current_count": null, "target_count": 1, "attribute_id_list": [6], "skill_id_list": [2, 4], "status": "COMPLETED", "priority": 3, "is_timer_active": false, "subtasks": null, "location": null}, "3": {"id": 3, "routine_id": 2, "title": "☕Baristacılık", "description": null, "type": "CHECKBOX", "task_date": "2025-04-24T18:08:43.244", "time": null, "is_notification_on": false, "is_alarm_on": false, "current_duration": null, "remaining_duration": "09:00:00", "current_count": null, "target_count": 1, "attribute_id_list": [7], "skill_id_list": [], "status": "COMPLETED", "priority": 3, "is_timer_active": null, "subtasks": null, "location": null}, "5": {"id": 5, "routine_id": 3, "title": "🍽️Öne Meal", "description": null, "type": "CHECKBOX", "task_date": "2025-04-24T23:04:44.953", "time": null, "is_notification_on": false, "is_alarm_on": false, "current_duration": null, "remaining_duration": "00:00:00", "current_count": null, "target_count": 1, "attribute_id_list": [8], "skill_id_list": [], "status": "FAILED", "priority": 3, "is_timer_active": null, "subtasks": null, "location": null}, "6": {"id": 6, "routine_id": 1, "title": "🧘Meditation", "description": null, "type": "TIMER", "task_date": "2025-04-25T23:04:44.958", "time": null, "is_notification_on": false, "is_alarm_on": false, "current_duration": "00:00:00", "remaining_duration": "00:10:00", "current_count": null, "target_count": 1, "attribute_id_list": [6], "skill_id_list": [2, 4], "status": null, "priority": 3, "is_timer_active": false, "subtasks": null, "location": null}, "7": {"id": 7, "routine_id": 2, "title": "☕Baristacılık", "description": null, "type": "CHECKBOX", "task_date": "2025-04-25T23:04:44.958", "time": null, "is_notification_on": false, "is_alarm_on": false, "current_duration": null, "remaining_duration": "09:00:00", "current_count": null, "target_count": 1, "attribute_id_list": [7], "skill_id_list": [], "status": null, "priority": 3, "is_timer_active": null, "subtasks": null, "location": null}, "8": {"id": 8, "routine_id": 3, "title": "🍽️Öne Meal", "description": null, "type": "CHECKBOX", "task_date": "2025-04-25T23:04:44.958", "time": null, "is_notification_on": false, "is_alarm_on": false, "current_duration": null, "remaining_duration": "00:00:00", "current_count": null, "target_count": 1, "attribute_id_list": [8], "skill_id_list": [], "status": null, "priority": 3, "is_timer_active": null, "subtasks": null, "location": null}}, "SharedPreferances": {"lastLoginDate": "2025-04-25T11:29:34.574890", "last_task_id": 8, "last_routine_id": 3, "last_trait_id": 8}}